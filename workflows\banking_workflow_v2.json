{"workflow": {"id": "banking_customer_service", "name": "Banking Customer Service", "version": "2.1", "start": "Greeting", "allowed_actions": ["Check account balance", "Transfer funds", "Get exchange rates", "Multiple requests in same session"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification"], "engagement_messages": "Is there anything else I can help you with today?", "interrupt_config": {"global_settings": {"enabled": false, "vad_threshold": 0.05, "confirmation_window_seconds": 0.5, "min_interrupt_duration_seconds": 0.3, "tts_interrupt_cooldown_seconds": 0, "vad_method": "webrtcvad", "webrtc_aggressiveness": 3, "required_consecutive_frames": 5, "user_speech_end_silence_seconds": 1.5}}, "states": {"Greeting": {"id": "Greeting", "type": "input", "layer2_id": "l2_greeting_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Inquiry"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me finish my greeting, then I'll help you."}}, "Inquiry": {"id": "Inquiry", "type": "inform", "layer2_id": "l2_inquiry_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferPhase"}, {"condition": "intent == 'exchange_rate'", "target": "exchangeRate"}, {"condition": "intent == 'goodbye'", "target": "Goodbye"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me finish my response, then I'll help you."}}, "RepeatedUserQuery": {"id": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "inform", "layer2_id": "l2_repeated_user_query_simple", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferPhase"}, {"condition": "intent == 'exchange_rate'", "target": "exchangeRate"}, {"condition": "intent == 'undo'", "target": "last_anti_state"}, {"condition": "intent == 'goodbye'", "target": "Goodbye"}, {"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["STT", "LLM", "TTS"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me understand your request completely."}}, "CheckBalance": {"id": "CheckBalance", "type": "inform", "layer2_id": "l2_check_balance_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS", "account_id"], "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Let me finish checking your balance."}}, "TransferFunds": {"id": "TransferFunds", "type": "transaction", "layer2_id": "l2_transfer_funds_banking_system_v2", "anti_state": "ReverseTransferFunds", "expected_input": ["account_id", "amount", "currency"], "expected_output": ["audio_path", "latencyTTS"], "missing_input_fallback_state": "TransferFundsInputFallback", "transitions": [{"condition": "amount >= 60000", "target": "Goodbye"}, {"condition": "balance < amount", "target": "CheckBalance"}, {"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please let me complete the transfer, then I'll help you."}}, "ReverseTransferFunds": {"id": "ReverseTransferFunds", "type": "transaction", "layer2_id": "l2_reverse_transfer_funds", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'confirm'", "target": "ConfirmReverse"}, {"condition": "intent == 'cancel'", "target": "CancelReverse"}, {"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "interrupt_config": {"enabled": true, "reversible": false, "interrupt_message": "Please let me complete the reversal."}}, "ConfirmReverse": {"id": "ConfirmReverse", "type": "confirm", "layer2_id": "l2_confirm_reverse", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["TTS"], "interrupt_config": {"enabled": true, "reversible": false, "interrupt_message": "Please confirm if you want to proceed with the reversal."}}, "CancelReverse": {"id": "CancelReverse", "type": "cancel", "layer2_id": "l2_cancel_reverse", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["TTS"], "interrupt_config": {"enabled": true, "reversible": false, "interrupt_message": "Please confirm if you want to proceed with the reversal."}}, "TransferFundsInputFallback": {"id": "TransferFundsInputFallback", "type": "input", "layer2_id": "l2_transfer_funds_input_fallback_banking_system_v2", "isFallbackInput": true, "expected_input": [], "expected_output": ["audio_path", "latencyTTS", "account_id", "amount", "currency"], "transitions": [{"condition": "true", "target": "TransferFunds"}], "allowed_tools": ["STT", "LLM", "TTS"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Please provide the account details for the transfer."}}, "exchangeRate": {"id": "exchangeRate", "type": "inform", "layer2_id": "l2_exchange_rate_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_tools": ["LLM", "TTS", "EXCHANGE_API"], "interrupt_config": {"enabled": true, "reversible": true, "interrupt_message": "Let me get the current exchange rates for you."}}, "Goodbye": {"id": "Goodbye", "type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["TTS"], "interrupt_config": {"enabled": false, "reversible": false, "interrupt_message": "Thank you for using our service."}}}, "phases": {"TransferPhase": {"name": "TransferPhase", "states": ["exchangeRate", "TransferFundsInputFallback", "TransferFunds"], "initial_state": "exchangeRate", "transitions": [{"condition": "true", "target": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}}}}