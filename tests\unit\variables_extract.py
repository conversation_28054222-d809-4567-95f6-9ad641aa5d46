import re
from typing import List

def extract_variables_from_condition(condition: str) -> List[str]:
        """
        Simple method to extract variable names from a condition string.

        Args:
            condition (str): The condition string (e.g., "amount >= 60000 and balance > 1000")

        Returns:
            List[str]: List of variable names found in the condition
        """
        if condition in ["true", "false"]:
            return []

        # Simple regex to find variable names (letters, numbers, underscore)
        # Exclude common keywords and operators
        variable_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\b'
        matches = re.findall(variable_pattern, condition)

        # Filter out Python keywords and operators
        excluded = {
            'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None',
            'if', 'else', 'elif', 'for', 'while', 'def', 'class'
        }

        variables = [var for var in matches if var not in excluded]
        return variables

if __name__ == "__main__":
    condition = "amount >= 60000 and balance > 1000"
    variables = extract_variables_from_condition(condition)
    print(variables)