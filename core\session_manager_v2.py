import asyncio
import uuid
import os
import time
from datetime import datetime
from typing import Optional, Dict, Any, TYPE_CHECKING

from core.audio.audio_player import AudioPlayer
from core.memory.redis_context import RedisClient
from core.state_manager.state_manager import StateManager
# MemoryManager is created by StateManager, no direct import needed

from core.logging.logger_config import get_module_logger


class SessionManagerV2:
    """
    Session Manager v2 - Complete session lifecycle management with clear architectural ownership.
    
    This manager owns and coordinates:
    1. Session creation and ID generation
    2. Component initialization (Orchestrator v3, Memory Manager, State Manager)
    3. Session state and context management
    4. Dialog saving throughout session lifecycle
    5. Comprehensive cleanup of all managed entities
    6. Graceful system shutdown
    
    Key architectural principles:
    - Single source of truth for session lifecycle
    - Dependency injection for all components
    - Clear cleanup order and resource management
    - No resource leaks or orphaned processes
    """
    
    def __init__(self, redis_url: str = None):
        """
        Initialize Session Manager v2.
        
        Args:
            redis_url: Redis connection URL (defaults to env var or localhost)
        """
        self.redis_url = redis_url or os.getenv("VOICE_AGENT_REDIS_URL", "redis://localhost:6379")
        self.redis_client = RedisClient(url=self.redis_url)
        self.logger = get_module_logger("session_manager_v2")
        
        # Session tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.is_running = True
        
        # Component references for cleanup
        self._orchestrator = None
        self._cleanup_tasks = []
        self.audioPlayer = None

        self.logger.log(
            level="info",
            message="SessionManagerV2 initialized",
            action="initialize",
            layer="session_manager_v2",
            step="init",
            status="success"
        )
    
    async def create_session(self, workflow_name: str, user_id: Optional[str] = None) -> str:
        """
        Create a new session with proper initialization of all components.

        Args:
            workflow_name: Name of the workflow to execute
            user_id: Optional user identifier

        Returns:
            str: Generated session ID

        Raises:
            Exception: If session creation fails
        """
        # Generate unique session ID and start timing
        session_id = f"session_{uuid.uuid4().hex[:12]}_{int(datetime.now().timestamp())}"
        start_time = time.perf_counter()

        try:
            self.logger.log(
                level="info",
                message="Creating new session",
                action="create_session",
                input_data={"session_id": session_id, "workflow_name": workflow_name, "user_id": user_id},
                layer="session_manager_v2",
                step="start_creation"
            )
            
            self.audioPlayer: AudioPlayer = AudioPlayer(session_id)

            # Step 1: Create State Manager (it will create its own Memory Manager)
            state_manager = await StateManager.create(
                workflow_name=workflow_name,
                session_id=session_id,
                user_id=user_id,
                audioPlayer=self.audioPlayer
            )
            
            await state_manager.memory_manager.clear_contextual()

            asyncio.create_task(self.audioPlayer.playAudio())


            # Step 2: Initialize Orchestrator v3 (new version, not v33)
            # Always import from agents/orchestrator_agent_v3.py
            from agents.orchestration.orchestrator_agent_v3 import OrchestratorV3
            orchestrator = OrchestratorV3(
                session_id=session_id,
                workflow_name=workflow_name,
                state_manager=state_manager,
                memory_manager=state_manager.memory_manager,
                redis_client=self.redis_client,
                apm=self.audioPlayer
            )
            
            # Step 3: Initialize session metadata in contextual memory
            await self._initialize_session_metadata(state_manager, workflow_name, user_id)
            
            # Step 4: Store session information
            session_info = {
                "session_id": session_id,
                "workflow_name": workflow_name,
                "user_id": user_id,
                "state_manager": state_manager,
                "memory_manager": state_manager.memory_manager,
                "orchestrator": orchestrator,
                "created_at": datetime.now().isoformat(),
                "status": "active"
            }
            
            self.active_sessions[session_id] = session_info

            # Calculate session creation duration
            duration_ms = (time.perf_counter() - start_time) * 1000

            self.logger.log(
                level="info",
                message="Session created successfully",
                action="create_session",
                input_data={"session_id": session_id, "workflow_name": workflow_name},
                output_data={"current_state": state_manager.current_workflow_state_id},
                status="success",
                layer="session_manager_v2",
                step="session_created",
                metrics={"duration_ms": duration_ms}
            )

            return session_id
            
        except Exception as e:
            # Calculate duration even on error
            duration_ms = (time.perf_counter() - start_time) * 1000

            self.logger.log(
                level="error",
                message="Failed to create session",
                action="create_session",
                input_data={"session_id": session_id, "workflow_name": workflow_name},
                reason=str(e),
                status="error",
                layer="session_manager_v2",
                step="creation_failed",
                metrics={"duration_ms": duration_ms}
            )
            # Clean up any partially created resources
            await self._cleanup_failed_session(session_id)
            raise
    
    async def _initialize_session_metadata(self, state_manager: StateManager, workflow_name: str, user_id: Optional[str]):
        """
        Initialize session metadata in contextual memory.

        Args:
            state_manager: State Manager instance
            workflow_name: Workflow name
            user_id: Optional user ID
        """
        try:
            memory_manager = state_manager.memory_manager

            # Store session metadata
            await memory_manager.set("contextual", "session_manager_version", "v2")
            await memory_manager.set("contextual", "workflow_name", workflow_name)
            await memory_manager.set("contextual", "user_id", user_id)
            await memory_manager.set("contextual", "session_start_time", datetime.now().isoformat())
            await memory_manager.set("contextual", "session_status", "active")
            await memory_manager.set("contextual", "managed_by", "session_manager_v2")

            # Load and store engagement messages from workflow configuration
            await self._load_engagement_messages(state_manager, memory_manager)

            self.logger.debug(
                "Session metadata initialized",
                action="initialize_session_metadata",
                input_data={"workflow_name": workflow_name, "user_id": user_id},
                layer="session_manager_v2",
                step="metadata_init"
            )

        except Exception as e:
            self.logger.error(
                "Failed to initialize session metadata",
                action="initialize_session_metadata",
                reason=str(e),
                layer="session_manager_v2",
                step="metadata_init_failed"
            )
            raise

    async def _load_engagement_messages(self, state_manager: StateManager, memory_manager):
        """
        Load engagement messages from workflow configuration and store them in memory.

        Args:
            state_manager: State Manager instance
            memory_manager: Memory Manager instance
        """
        try:
            workflow = await state_manager.getWorkflow()
            if not workflow or not workflow.workflow:
                self.logger.warning(
                    "No workflow found for engagement message loading",
                    action="load_engagement_messages",
                    layer="session_manager_v2"
                )
                return

            # Get engagement message from workflow configuration with fallback
            engagement_message = getattr(workflow.workflow, 'engagement_messages', None)
            if not engagement_message:
                engagement_message = "Is there anything else I can help you with today?"
                self.logger.info(
                    "No engagement message found in workflow configuration, using fallback",
                    action="load_engagement_messages",
                    layer="session_manager_v2"
                )

            # Store engagement message in memory for RepeatedUserQuery state
            session_id = state_manager.session_id
            await memory_manager.set("contextual", f"{session_id}_RepeatedUserQuery_engagement_message", engagement_message)

            self.logger.info(
                "Engagement message loaded and stored in memory",
                action="load_engagement_messages",
                input_data={"engagement_message": engagement_message},
                layer="session_manager_v2"
            )

        except Exception as e:
            self.logger.error(
                "Failed to load engagement messages from workflow configuration",
                action="load_engagement_messages",
                reason=str(e),
                layer="session_manager_v2"
            )
            # Don't raise - this is not critical for session creation

    async def initialize_orchestrator(self, session_id: str):
        """
        Initialize Orchestrator v3 for a session with proper dependency injection.

        Args:
            session_id: Session ID to initialize orchestrator for

        Returns:
            OrchestratorV3: Initialized orchestrator instance

        Raises:
            ValueError: If session doesn't exist
            Exception: If orchestrator initialization fails
        """
        # Now just returns the orchestrator from active_sessions
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        orchestrator = self.active_sessions[session_id]["orchestrator"]
        # Optionally, ensure orchestrator has a run method
        if not hasattr(orchestrator, "run") or not asyncio.iscoroutinefunction(orchestrator.run):
            raise AttributeError("OrchestratorV3 does not have an async run() method.")
        return orchestrator

    async def notify_workflow_complete(self, session_id: str):
        """
        Called by orchestrator when workflow is complete.
        Persists all session data and logs, then cleans up.
        """
        await self.save_dialog_log(session_id)
        await self.cleanup_session(session_id, reason="workflow_complete")

    async def save_dialog_log(self, session_id: str) -> bool:
        """
        Save dialog log for a session throughout the session lifecycle.

        Args:
            session_id: Session ID to save dialog for

        Returns:
            bool: True if successful, False otherwise
        """
        if session_id not in self.active_sessions:
            self.logger.warning(
                "Cannot save dialog log for non-existent session",
                action="save_dialog_log",
                input_data={"session_id": session_id},
                layer="session_manager_v2",
                step="session_not_found"
            )
            return False

        try:
            session_info = self.active_sessions[session_id]
            memory_manager = session_info["memory_manager"]

            # Save persistent memory data (includes dialog log)
            await memory_manager.save_persistent_memory_data()

            self.logger.info(
                "Dialog log saved successfully",
                action="save_dialog_log",
                input_data={"session_id": session_id},
                status="success",
                layer="session_manager_v2",
                step="dialog_saved"
            )

            return True

        except Exception as e:
            self.logger.error(
                "Failed to save dialog log",
                action="save_dialog_log",
                input_data={"session_id": session_id},
                reason=str(e),
                status="error",
                layer="session_manager_v2",
                step="dialog_save_failed"
            )
            return False

    async def cleanup_session(self, session_id: str, reason: str = "session_complete") -> bool:
        """
        Perform comprehensive cleanup of a session and all its managed entities.

        Cleanup order (critical for proper resource management):
        1. Memory Manager cleanup (save persistent data, clear caches)
        2. State Manager cleanup (end session, clear state)
        3. Orchestrator cleanup (stop listeners, clear session data)
        4. Redis cleanup (remove session keys)
        5. Remove from active sessions

        Args:
            session_id: Session ID to cleanup
            reason: Reason for cleanup (for logging)

        Returns:
            bool: True if cleanup successful, False otherwise
        """
        while self.audioPlayer.turn == "ai" or self.audioPlayer.isAudioPlaying():
            await asyncio.sleep(0.1)

        if session_id not in self.active_sessions:
            self.logger.warning(
                "Cannot cleanup non-existent session",
                action="cleanup_session",
                input_data={"session_id": session_id, "reason": reason},
                layer="session_manager_v2",
                step="session_not_found"
            )
            return False

        session_info = self.active_sessions[session_id]
        cleanup_success = True
        cleanup_start_time = time.perf_counter()

        try:
            self.logger.info(
                "Starting comprehensive session cleanup",
                action="cleanup_session",
                input_data={"session_id": session_id, "reason": reason},
                layer="session_manager_v2",
                step="cleanup_start"
            )

            # Set session end time in memory before cleanup
            memory_manager = session_info.get("memory_manager")
            if memory_manager:
                await memory_manager.set("contextual", "session_end_time", datetime.now().isoformat())

            # Step 1: Memory Manager cleanup (save data first)
            try:
                if memory_manager:
                    await memory_manager.save_persistent_memory_data()
                    await memory_manager.clear_ephemeral()
                    self.logger.debug("Memory Manager cleanup completed", action="cleanup_session", layer="session_manager_v2", step="memory_cleanup")
            except Exception as e:
                self.logger.error("Memory Manager cleanup failed", action="cleanup_session", reason=str(e), layer="session_manager_v2", step="memory_cleanup_failed")
                cleanup_success = False

            # Step 2: State Manager cleanup
            try:
                state_manager = session_info.get("state_manager")
                if state_manager:
                    await state_manager.end_session_cleanup()
                    self.logger.debug("State Manager cleanup completed", action="cleanup_session", layer="session_manager_v2", step="state_cleanup")
            except Exception as e:
                self.logger.error("State Manager cleanup failed", action="cleanup_session", reason=str(e), layer="session_manager_v2", step="state_cleanup_failed")
                cleanup_success = False

            # Step 3: Orchestrator cleanup
            try:
                orchestrator = session_info.get("orchestrator")
                if orchestrator:
                    await orchestrator.cleanup()
                    self.logger.debug("Orchestrator cleanup completed", action="cleanup_session", layer="session_manager_v2", step="orchestrator_cleanup")
            except Exception as e:
                self.logger.error("Orchestrator cleanup failed", action="cleanup_session", reason=str(e), layer="session_manager_v2", step="orchestrator_cleanup_failed")
                cleanup_success = False

            # Step 4: Redis cleanup (remove session keys)
            try:
                await self._cleanup_redis_session_data(session_id)
                self.logger.debug("Redis cleanup completed", action="cleanup_session", layer="session_manager_v2", step="redis_cleanup")
            except Exception as e:
                self.logger.error("Redis cleanup failed", action="cleanup_session", reason=str(e), layer="session_manager_v2", step="redis_cleanup_failed")
                cleanup_success = False

            # Step 5: Remove from active sessions
            try:
                session_info["status"] = "cleaned_up"
                del self.active_sessions[session_id]
                self.logger.debug("Session removed from active sessions", action="cleanup_session", layer="session_manager_v2", step="session_removal")
            except Exception as e:
                self.logger.error("Failed to remove session from active sessions", action="cleanup_session", reason=str(e), layer="session_manager_v2", step="session_removal_failed")
                cleanup_success = False

            # Calculate cleanup duration
            cleanup_duration_ms = (time.perf_counter() - cleanup_start_time) * 1000

            if cleanup_success:
                self.logger.info(
                    "Session cleanup completed successfully",
                    action="cleanup_session",
                    input_data={"session_id": session_id, "reason": reason},
                    status="success",
                    layer="session_manager_v2",
                    step="cleanup_complete",
                    metrics={"duration_ms": cleanup_duration_ms}
                )
            else:
                self.logger.warning(
                    "Session cleanup completed with some failures",
                    action="cleanup_session",
                    input_data={"session_id": session_id, "reason": reason},
                    status="partial_success",
                    layer="session_manager_v2",
                    step="cleanup_partial",
                    metrics={"duration_ms": cleanup_duration_ms}
                )

            return cleanup_success

        except Exception as e:
            # Calculate duration even on error
            cleanup_duration_ms = (time.perf_counter() - cleanup_start_time) * 1000

            self.logger.error(
                "Critical error during session cleanup",
                action="cleanup_session",
                input_data={"session_id": session_id, "reason": reason},
                reason=str(e),
                status="error",
                layer="session_manager_v2",
                step="cleanup_critical_error",
                metrics={"duration_ms": cleanup_duration_ms}
            )
            return False

    async def _cleanup_redis_session_data(self, session_id: str):
        """
        Clean up Redis session data using scan to avoid blocking.

        Args:
            session_id: Session ID to cleanup Redis data for
        """
        try:
            # Use scan instead of keys to avoid blocking Redis
            keys = []
            cursor = 0
            while True:
                cursor, batch = await self.redis_client.client.scan(
                    cursor, match=f"session:{session_id}:*", count=100
                )
                keys.extend(batch)
                if cursor == 0:
                    break

            if keys:
                await self.redis_client.delete(*keys)
                self.logger.debug(
                    f"Cleaned up {len(keys)} Redis keys for session",
                    action="cleanup_redis_session_data",
                    input_data={"session_id": session_id, "keys_count": len(keys)},
                    layer="session_manager_v2",
                    step="redis_keys_deleted"
                )

        except Exception as e:
            self.logger.error(
                "Failed to cleanup Redis session data",
                action="cleanup_redis_session_data",
                input_data={"session_id": session_id},
                reason=str(e),
                layer="session_manager_v2",
                step="redis_cleanup_failed"
            )
            raise

    async def _cleanup_failed_session(self, session_id: str):
        """
        Clean up resources from a failed session creation.

        Args:
            session_id: Session ID to cleanup
        """
        try:
            # Remove from active sessions if it exists
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]

            # Clean up any Redis data that might have been created
            await self._cleanup_redis_session_data(session_id)

            self.logger.debug(
                "Failed session cleanup completed",
                action="cleanup_failed_session",
                input_data={"session_id": session_id},
                layer="session_manager_v2",
                step="failed_session_cleaned"
            )

        except Exception as e:
            self.logger.error(
                "Error during failed session cleanup",
                action="cleanup_failed_session",
                input_data={"session_id": session_id},
                reason=str(e),
                layer="session_manager_v2",
                step="failed_cleanup_error"
            )

    async def shutdown(self):
        """
        Gracefully shutdown the entire system after cleanup completion.

        This method:
        1. Cleans up all active sessions
        2. Closes Redis connections
        3. Stops all background tasks
        4. Ensures no resource leaks or orphaned processes
        """
        try:
            self.logger.info(
                "Starting system shutdown",
                action="shutdown",
                input_data={"active_sessions_count": len(self.active_sessions)},
                layer="session_manager_v2",
                step="shutdown_start"
            )

            self.is_running = False

            # Step 1: Cleanup all active sessions
            session_ids = list(self.active_sessions.keys())
            for session_id in session_ids:
                try:
                    await self.cleanup_session(session_id, "system_shutdown")
                except Exception as e:
                    self.logger.error(
                        f"Failed to cleanup session {session_id} during shutdown",
                        action="shutdown",
                        reason=str(e),
                        layer="session_manager_v2",
                        step="session_cleanup_failed"
                    )

            # Step 2: Cancel cleanup tasks
            for task in self._cleanup_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            # Step 3: Close Redis connection
            try:
                await self.redis_client.close()
                self.logger.debug("Redis connection closed", action="shutdown", layer="session_manager_v2", step="redis_closed")
            except Exception as e:
                self.logger.error("Failed to close Redis connection", action="shutdown", reason=str(e), layer="session_manager_v2", step="redis_close_failed")

            # Step 4: Close orchestrator if exists
            if self._orchestrator:
                try:
                    await self._orchestrator.close()
                    self.logger.debug("Orchestrator closed", action="shutdown", layer="session_manager_v2", step="orchestrator_closed")
                except Exception as e:
                    self.logger.error("Failed to close orchestrator", action="shutdown", reason=str(e), layer="session_manager_v2", step="orchestrator_close_failed")

            self.logger.info(
                "System shutdown completed",
                action="shutdown",
                status="success",
                layer="session_manager_v2",
                step="shutdown_complete"
            )

        except Exception as e:
            self.logger.error(
                "Critical error during system shutdown",
                action="shutdown",
                reason=str(e),
                status="error",
                layer="session_manager_v2",
                step="shutdown_critical_error"
            )
            raise

    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session information.

        Args:
            session_id: Session ID to get info for

        Returns:
            Dict containing session info or None if not found
        """
        session_info = self.active_sessions.get(session_id)
        if session_info:
            # Return a copy without the actual objects for safety
            return {
                "session_id": session_info["session_id"],
                "workflow_name": session_info["workflow_name"],
                "user_id": session_info["user_id"],
                "created_at": session_info["created_at"],
                "status": session_info["status"]
            }
        return None

    def list_active_sessions(self) -> list:
        """
        List all active session IDs.

        Returns:
            List of active session IDs
        """
        return list(self.active_sessions.keys())
