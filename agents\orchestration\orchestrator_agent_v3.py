import asyncio
import json
import os
import queue
from random import randrange
import threading
import time
import re
from typing import Dict, Any, Optional, List
from core.audio.audio_player import AudioPlayer
from core.memory.memory_manager import MemoryManager
from core.memory.redis_context import RedisClient
from core.logging.logger_config import get_module_logger
import openai

from core.state_manager.state_manager import StateManager
from schemas.audio_schema import AudioSchema, AudioTag
from schemas.outputSchema import StatusCode, StatusType

class OrchestratorV3:
    def __init__(self, session_id: str, workflow_name: str, state_manager : StateManager , memory_manager : MemoryManager, redis_client: RedisClient, apm: AudioPlayer):
        self.session_id = session_id
        self.workflow_name = workflow_name
        self.state_manager : StateManager = state_manager
        self.memory_manager : MemoryManager = memory_manager
        self.redis_client = redis_client
        self.logger = get_module_logger("orchestrator_v3", session_id=session_id)
        self.prohibited_actions = None
        self.agent_response_cache: Dict[str, List[Dict[str, Any]]] = {}  # state_id -> list of responses
        self.workflow_summary = None
        self.status = "initialized"
        self.reason = None
        self.key_events = []
        # self.communication_queue = queue.Queue()
        self.messageId = 0
        self.audioPlayer: AudioPlayer = apm

        # Track conversation for dialog logging
        self.user_message = ""
        self.ai_response = ""

    async def initialize(self):
        # Cache prohibited actions at start
        if self.prohibited_actions is None:
            self.prohibited_actions = await self.state_manager.getProhibitedActions()
        # Preload agent responses for this session
        await self._populate_agent_response_cache()
        self.logger.info("OrchestratorV3 initialized", action="initialize", layer="orchestrator_v3", step="init")

    async def _populate_agent_response_cache(self):
        """Parse the conversations log and cache agent responses for this session."""
        log_path = os.path.join("logs", "conversations", "conversations.jsonl")
        if not os.path.exists(log_path):
            return
        self.agent_response_cache.clear()
        try:
            with open(log_path, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line)
                        if entry.get("session_id") == self.session_id:
                            state_id = entry.get("state_id", "unknown")
                            if state_id not in self.agent_response_cache:
                                self.agent_response_cache[state_id] = []
                            self.agent_response_cache[state_id].append(entry)
                    except Exception:
                        continue
        except Exception as e:
            self.logger.error(f"Failed to populate agent response cache: {e}", action="populate_agent_response_cache", layer="orchestrator_v3")

    # [CLEANUP] does not get called in here
    async def get_user_query(self, retries=3) -> Optional[str]:
        for attempt in range(retries):
            clean_text = await self.memory_manager.get("clean_text")
            if clean_text:
                return clean_text
            await asyncio.sleep(0.5)
        self.logger.error("User query (clean_text) not found after retries", action="get_user_query", layer="orchestrator_v3")
        return None
        
    # [CLEANUP] does not get called in here
    async def get_agent_responses(self, agent_name: str) -> List[str]:
        # Return all agent outputs for this state from the cache
        responses = []
        for entry in self.agent_response_cache.get(agent_name, []):
            output = entry.get("output")
            if output:
                if isinstance(output, dict) and "llm_answer" in output:
                    responses.append(output["llm_answer"])
                elif isinstance(output, str):
                    responses.append(output)
        return responses

    async def get_agent_confidence(self, agent_name: str) -> Optional[float]:
        # Try to get confidence from Redis (publish/subscribe or key)
        try:
            key = f"session:{self.session_id}:agent:{agent_name}:confidence"
            confidence = await self.redis_client.get(key)
            if confidence is not None:
                try:
                    return float(confidence)
                except Exception:
                    return None
            self.logger.warning(f"Confidence value missing for agent {agent_name}", action="get_agent_confidence", layer="orchestrator_v3")
        except Exception as e:
            self.logger.warning(f"Error retrieving confidence for agent {agent_name}: {e}", action="get_agent_confidence", layer="orchestrator_v3")
        return None
    # [CLEANUP] remove call down we do not need it it is being used just for print
    async def get_state_summary(self) -> str:
        # Get workflow and pipeline state summaries
        workflow_state = await self.state_manager.getCurrentWorkflowState()
        pipeline_state = await self.state_manager.getCurrentPipelineState()
        allowed_actions = await self.state_manager.getAllowedActions()
        summary = (
            f"Workflow: {self.workflow_name}\n"
            f"Current Workflow State: {workflow_state}\n"
            f"Current Pipeline State: {pipeline_state}\n"
            f"Allowed Actions: {allowed_actions}\n"
            f"Prohibited Actions: {self.prohibited_actions}"
        )
        return summary

    # [CLEANUP] does not get called in here
    async def evaluate_with_llm(self, user_query: str, agent_responses: List[str], agent_confidence: Optional[float], state_summary: str) -> str:
        start_time = time.perf_counter()

        prompt = (
            "You are an AI orchestrator evaluating a conversation state. Based on the following information, decide whether to PROCEED to the next state or REDO the current state.\n"
            f"State Summary: {state_summary}\n"
            f"User Query: {user_query if user_query is not None else '[Not available]'}\n"
            f"Agent Responses: {agent_responses}\n"
            f"Agent Confidence: {agent_confidence}\n"
            "If the user query is not available, make your best decision based on the other information.\n"
            "Respond with ONLY one word: 'proceed' or 'redo'."
        )
        print("[DEBUG] LLM prompt:", prompt)
        print("[DEBUG] LLM inputs:", {
            "user_query": user_query,
            "agent_responses": agent_responses,
            "agent_confidence": agent_confidence,
            "state_summary": state_summary
        })
        try:
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=100,
                temperature=0.1
            )
            decision = response.choices[0].message.content.strip().lower()

            duration_ms = (time.perf_counter() - start_time) * 1000
            self.logger.info(
                "LLM evaluation completed",
                action="evaluate_with_llm",
                input_data={"user_query": user_query, "agent_confidence": agent_confidence},
                output_data={"decision": decision},
                layer="orchestrator_v3",
                metrics={"duration_ms": duration_ms}
            )

            print("[DEBUG] LLM raw response:", response)
            return "proceed" if "proceed" in decision else "redo"
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            print(f"[DEBUG] LLM evaluation failed, defaulting to 'redo': {e}")
            self.logger.error(
                f"LLM evaluation failed: {e}",
                action="evaluate_with_llm",
                layer="orchestrator_v3",
                metrics={"duration_ms": duration_ms}
            )
            return "redo"

    async def back_channeling_worker(self, messageId: int):
        print(f"[Worker {messageId}] Started")
        for i in range(3):
            if i == 0:
                await asyncio.sleep(0.5)
            else:
                await asyncio.sleep(3)
            filler_tts_output = await self.state_manager.executeBackChanneling()
            if filler_tts_output.status == StatusType.SUCCESS and filler_tts_output.outputs.get("audio_path"):
                print(f"Worker {messageId} processed item {i} -- {filler_tts_output.outputs.get('audio_path')}")
                await self.audioPlayer.enqueue(AudioSchema(audio_path=filler_tts_output.outputs.get("audio_path"), tag=AudioTag.filler, id=messageId))
            print(f"[Worker {messageId}] Processed item {i}")
        print(f"[Worker {messageId}] Signaling self-stop")

    async def run(self):
        orchestrator_start_time = time.perf_counter()

        self.logger.info(
            "Starting orchestrator execution",
            action="run",
            input_data={"session_id": self.session_id, "workflow_name": self.workflow_name},
            layer="orchestrator_v3"
        )

        await self.initialize()
        self.status = "running"
        user_query_found = False
        if await self.state_manager.getCurrentPipelineState() == "tts":
            await self.audioPlayer.incrementUserTurnMessageId()
        try:
            while True:
                print(f"[kekw] current workflow state: ", await self.state_manager.getCurrentWorkflowState())
                print(f"[kekw] current pipeline state: ", await self.state_manager.getCurrentPipelineState())
                # Always re-fetch the current pipeline state and pipeline at the start of each iteration
                state_summary = await self.get_state_summary()
                pipeline_state = await self.state_manager.getCurrentPipelineState()
                raw_pipeline = await self.state_manager.getCurrentPipeline()
                if hasattr(raw_pipeline, 'pipeline'):
                    current_pipeline = raw_pipeline.pipeline
                else:
                    current_pipeline = raw_pipeline
                if current_pipeline is None or not hasattr(current_pipeline, '__iter__'):
                    self.logger.error("Current pipeline is None or not iterable. Aborting.")
                    return {'status': 'aborted', 'reason': 'Current pipeline is None or not iterable.'}
                if pipeline_state is None:
                    self.logger.error("Pipeline state is None. Aborting.")
                    return {'status': 'aborted', 'reason': 'Pipeline state is None.'}
                current_step = None
                for step in current_pipeline:
                    if step is None:
                        self.logger.warning("Encountered None in current_pipeline steps. Skipping.")
                        continue
                    if hasattr(step, "step") and step.step == pipeline_state:
                        current_step = step
                        break
                if not current_step:
                    self.logger.error(f"No pipeline step found for state: {pipeline_state}. Aborting.")
                    return {'status': 'aborted', 'reason': f'No pipeline step found for state: {pipeline_state}'}
                agent_names = [current_step.agent] if isinstance(current_step.agent, str) else list(current_step.agent)

                # Get current workflow state to determine context
                workflow_state = await self.state_manager.getCurrentWorkflowState()

                # Handle special input cases - provide input data directly, don't set memory
                input_data = {}
                if workflow_state == "Greeting" and current_step.step == "tts":
                    # For greeting TTS, provide the greeting message directly
                    input_data = {"text": "Hello, how can I assist you today v2?"}
                elif workflow_state == "Goodbye" and current_step.step == "tts":
                    # For goodbye TTS, provide the goodbye message directly
                    input_data = {"text": "Thank you for using our service. Goodbye!"}
                else:
                    # For all other steps, let StateManager handle input construction from memory
                    input_data = {}
                # Execute the pipeline step with retry logic
                retry_count = 0
                max_retries = 3
                while retry_count <= max_retries:
                    try:
                        if input_data is not None:
                            result = await self.state_manager.executePipelineState(input_data)
                            # input("Press Enter to continue...")
                        else:
                            result = await self.state_manager.executePipelineState()
                            # input("Press Enter to continue...")                            
                        await asyncio.sleep(0.1)
                        if await self.state_manager.getCurrentPipelineState() == "tts" and result.status == StatusType.SUCCESS:
                            await self.audioPlayer.enqueue(AudioSchema(audio_path=result.outputs.get("audio_path"), tag=AudioTag.main, id=self.messageId))
                        if await self.state_manager.getCurrentPipelineState() == "stt" and result.code == StatusCode.NO_CONTENT:
                            return {'status': 'aborted', 'reason': 'STT step requires audio_path in memory but none found'}                            
                        break
                    except Exception as e:
                        self.logger.error(f"Agent execution failed: {e}", action="executePipelineState", layer="orchestrator_v3")
                        retry_count += 1
                        if retry_count > max_retries:
                            self.logger.error(f"Agent failed after retry. Aborting.", action="agent_failure", layer="orchestrator_v3")
                            # ALERT: Could add alerting logic here (e.g., send notification)
                            return {'status': 'aborted', 'reason': f"Agent failed after retry: {e}"}
                output = None
                if isinstance(result, dict):
                    output = result.get('outputs') if 'outputs' in result else result.get('output') if 'output' in result else result
                elif hasattr(result, 'dict') and callable(getattr(result, 'dict')):
                    result_dict = result.dict()
                    output = result_dict.get('outputs') if 'outputs' in result_dict else result_dict.get('output') if 'output' in result_dict else result_dict
                elif hasattr(result, 'model_dump') and callable(getattr(result, 'model_dump')):
                    result_dict = result.model_dump()
                    output = result_dict.get('outputs') if 'outputs' in result_dict else result_dict.get('output') if 'output' in result_dict else result_dict
                else:
                    try:
                        output = dict(result)
                    except Exception as e:
                        self.logger.error(f"Agent output could not be converted to dict: {e}")
                        raise RuntimeError(f"Agent output could not be converted to dict: {e}")
                if output is None:
                    self.logger.warning(f"Agent output is None. Attempting to fetch from memory/context as fallback.")
                    output = {}
                    for out_key, mem_key in current_step.output.items():
                        mem_key_full = f"{self.session_id}_{await self.state_manager.getCurrentWorkflowState()}_{mem_key}"
                        value = await self.memory_manager.get(mem_key_full)
                        if value is not None:
                            output[out_key] = value
                    if not output:
                        self.logger.error(f"No output found in memory/context for step '{current_step.step}'. Aborting pipeline.")
                        raise RuntimeError(f"No output for step '{current_step.step}' in agent return or memory/context.")
                last_step_output = output
                if isinstance(output, dict):
                    for out_key, out_value in output.items():
                        await self.memory_manager.set("contextual", out_key, out_value)
                await self._track_conversation_turn(current_step.step, output)
                # Aggregate agent outputs and confidences for multi-agent support
                agent_outputs = []
                agent_confidences = []
                for agent_name in agent_names:
                    # Use the current step output instead of cache - format it nicely for LLM
                    if isinstance(output, dict):
                        # Format the output nicely for LLM evaluation
                        formatted_output = []
                        for key, value in output.items():
                            formatted_output.append(f"{key}: {value}")
                        agent_outputs.append(formatted_output)
                    else:
                        agent_outputs.append([str(output)])
                    conf_val = await self.get_agent_confidence(agent_name)
                    if conf_val is not None:
                        agent_confidences.append(conf_val)
                # Fetch user query and intent using correct memory key format
                workflow_state = await self.state_manager.getCurrentWorkflowState()
                user_query_key = f"{self.session_id}_{workflow_state}_clean_text"
                intent_key = f"{self.session_id}_{workflow_state}_intent"

                print(f"[DEBUG] Memory key lookup:")
                print(f"  Workflow State: {workflow_state}")
                print(f"  User Query Key: {user_query_key}")
                print(f"  Intent Key: {intent_key}")

                user_query = await self.memory_manager.get(user_query_key)
                intent = await self.memory_manager.get(intent_key)

                if user_query is None:
                    # Fallback to simple key
                    user_query = await self.memory_manager.get("clean_text")
                if intent is None:
                    # Fallback to simple key
                    intent = await self.memory_manager.get("intent")

                print(f"[DEBUG] Retrieved from memory:")
                print(f"  User Query (clean_text): {user_query}")
                print(f"  Intent: {intent}")
                print(f"  Agent Output: {output}")

                # Debug: Show all contextual memory keys
                all_contextual = await self.memory_manager.get_all_contextual()
                # Only evaluate after preprocessing/processing steps
                if current_step.step in ["preprocessing", "processing"]:
                    # If any confidence > 90, proceed
                    if any([c for c in agent_confidences if isinstance(c, (int, float)) and c > 0.9]):
                        next_step_id = None
                        currentPipeLineStepIndex = await self.state_manager.getCurrentPipelineStepIndex()
                        for idx, step in enumerate(current_pipeline):
                            if hasattr(step, "step") and step.step == pipeline_state and idx >= currentPipeLineStepIndex:
                                if idx + 1 < len(current_pipeline):
                                    next_step_id = current_pipeline[idx + 1].step
                                break
                        if next_step_id:
                            await self.state_manager.transitionPipeline(next_step_id)
                            print(f"current pipeline state: ", await self.state_manager.getCurrentPipelineState())
                            if await self.state_manager.getCurrentPipelineState() == "processing" and await self.state_manager.isBackChannelingEnabled():
                                print(f"[DEBUG] Back-channeling enabled, starting worker for message ID {self.messageId}")
                                asyncio.create_task(self.back_channeling_worker(self.messageId))
                                self.messageId += 1
                            if await self.state_manager.getCurrentPipelineState() == "tts":
                                await self.audioPlayer.incrementUserTurnMessageId()
                            continue
                        else:
                            # End of pipeline, handle workflow transition
                            workflow = await self.state_manager.getWorkflow()
                            workflow_obj = workflow.workflow
                            state_config = workflow_obj.states.get(workflow_state)

                            # Check if this is an end state with no transitions
                            if state_config and state_config.type == "end" and (not state_config.transitions or len(state_config.transitions) == 0):
                                self.status = "completed"
                                self.reason = f"Workflow completed successfully at end state: {workflow_state}"
                                self.logger.info(f"Workflow completed at end state: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                return {"status": self.status, "reason": self.reason, "key_events": self.key_events}

                            allowed_transitions = [t.target for t in state_config.transitions] if state_config and state_config.transitions else []
                            transitionsDict = state_config.transitions
                            transitionsDict = state_config.transitions 
                            possibleNextPhases = [await self.state_manager.getPhaseDataByName(ph) for ph in allowed_transitions if ph in workflow_obj.phases.keys()]
                            if await self.state_manager.getIsInPhase():
                                print(f"[kekw] await self.state_manager.getPhaseData() : ", await self.state_manager.getPhaseData())
                                phaseData = await self.state_manager.getPhaseData()
                                possibleNextPhases.append(await self.state_manager.getPhaseDataByName(phaseData["currentPhase"]))
                                allowed_transitions.append(phaseData["currentPhase"])                            
                            if not allowed_transitions:
                                # No transitions available, check if Goodbye state exists and auto-transition
                                workflow = await self.state_manager.getWorkflow()
                                workflow_obj = workflow.workflow
                                print(f"[DEBUG] No transitions from {workflow_state}. Available states: {list(workflow_obj.states.keys())}")
                                if "Goodbye" in workflow_obj.states:
                                    print(f"[DEBUG] No transitions available from {workflow_state}, auto-transitioning to Goodbye")
                                    await self.state_manager.transitionWorkflow("Goodbye")
                                    continue
                                else:
                                    # No Goodbye state, complete the workflow
                                    print(f"[DEBUG] No Goodbye state found, completing workflow")
                                    self.status = "completed"
                                    self.reason = f"Workflow completed - no transitions available from state: {workflow_state}"
                                    self.logger.info(f"Workflow completed - no transitions from: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                    return {"status": self.status, "reason": self.reason, "key_events": self.key_events}
                            else:
                                # Enhanced LLM prompt with variable context
                                variable_tuples = await self.get_variable_context_for_transitions(allowed_transitions, workflow_state)
                                variable_context_str = ", ".join([f"{var}={val}" for var, val in variable_tuples]) if variable_tuples else "No variables available"
                                # function that builds the prompt
                                llm_prompt = await self.build_workflow_transition_prompt(user_query, workflow_state, intent, variable_context_str, allowed_transitions, transitionsDict, self.state_manager, possibleNextPhases)
                                print(f"[mohamed] llm_prompt: {llm_prompt}")
                                decision = await self.evaluate_llm_workflow_transition(llm_prompt, allowed_transitions)
                                print(f"[kekw] llm prompt : {llm_prompt}")
                                print(f"[kekw] LLM decision: {decision}")
                                # if decision not in allowed_transitions:
                                #     next_workflow_state = "Goodbye"
                                # else:
                                #     next_workflow_state = decision
                                next_workflow_state = decision
                                await self.state_manager.transitionWorkflow(next_workflow_state)
                                print(f"current pipeline state: ", await self.state_manager.getCurrentPipelineState())
                                if await self.state_manager.getCurrentPipelineState() == "processing" and await self.state_manager.isBackChannelingEnabled():
                                    print(f"[DEBUG] Back-channeling enabled, starting worker for message ID {self.messageId}")
                                    asyncio.create_task(self.back_channeling_worker(self.messageId))
                                    self.messageId += 1
                                if await self.state_manager.getCurrentPipelineState() == "tts":
                                    await self.audioPlayer.incrementUserTurnMessageId()
                                    continue
                    else:
                        # LLM evaluation based only on intent vs user query match (no confidence values)
                        print(f"[DEBUG] Low confidence, using LLM evaluation based on intent match")
                        llm_prompt = f"""
You are an AI orchestrator evaluating whether to proceed to the next pipeline step or redo the current step.

Context:
- Pipeline Step: {current_step.step}
- User Query: {user_query}
- Detected Intent: {intent}

Instructions:
- Evaluate ONLY if the detected intent correctly matches what the user is asking for
- For preprocessing steps: If the user asks about "balance" and intent is "account_balance" → respond "proceed"
- For preprocessing steps: If the user asks about "transfer" and intent is "fund_transfer" → respond "proceed"
- For preprocessing steps: If the user asks about "payment" and intent is "payment" → respond "proceed"
- For preprocessing steps: If the user asks about "exchange" and intent is "exchange_rate" → respond "proceed"
- For preprocessing steps: If the user asks about "goodbye" and intent is "goodbye" → respond "proceed"
- For preprocessing steps: If the user asks about "undo" and intent is "undo" → respond "proceed"
- For preprocessing steps: If the user confirms (yes, confirm, proceed) and intent is "confirm" → respond "proceed"
- For preprocessing steps: If the user cancels (no, cancel, stop) and intent is "cancel" → respond "proceed"
- Only respond "redo" if the intent is completely wrong or missing
- Respond with ONLY one word: "proceed" or "redo"

Analysis:
- User Query: "{user_query}"
- Detected Intent: "{intent}"
- Does the intent match the user's request? If YES → "proceed", if NO → "redo"
"""
                        print(f"[DEBUG] LLM Evaluation Data:")
                        print(f"  Pipeline Step: {current_step.step}")
                        print(f"  User Query: {user_query}")
                        print(f"  Agent Outputs: {agent_outputs}")
                        print(f"  Agent Confidences: {agent_confidences}")
                        decision = await self.evaluate_llm_pipeline_transition(llm_prompt)
                        if decision == "proceed":
                            next_step_id = None
                            currentPipeLineStepIndex = await self.state_manager.getCurrentPipelineStepIndex()
                            for idx, step in enumerate(current_pipeline):
                                if hasattr(step, "step") and step.step == pipeline_state and idx >= currentPipeLineStepIndex:
                                    if idx + 1 < len(current_pipeline):
                                        next_step_id = current_pipeline[idx + 1].step
                                    break
                            if next_step_id:
                                await self.state_manager.transitionPipeline(next_step_id)
                                print(f"current pipeline state: ", await self.state_manager.getCurrentPipelineState())
                                if await self.state_manager.getCurrentPipelineState() == "processing" and await self.state_manager.isBackChannelingEnabled():
                                    print(f"[DEBUG] Back-channeling enabled, starting worker for message ID {self.messageId}")
                                    asyncio.create_task(self.back_channeling_worker(self.messageId))
                                    self.messageId += 1
                                if await self.state_manager.getCurrentPipelineState() == "tts":
                                    await self.audioPlayer.incrementUserTurnMessageId()
                                continue
                            else:
                                # End of pipeline, handle workflow transition
                                workflow = await self.state_manager.getWorkflow()
                                workflow_obj = workflow.workflow
                                state_config = workflow_obj.states.get(workflow_state)

                                # Check if this is an end state with no transitions
                                if state_config and state_config.type == "end" and (not state_config.transitions or len(state_config.transitions) == 0):
                                    self.status = "completed"
                                    self.reason = f"Workflow completed successfully at end state: {workflow_state}"
                                    self.logger.info(f"Workflow completed at end state: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                    return {"status": self.status, "reason": self.reason, "key_events": self.key_events}

                                allowed_transitions = [t.target for t in state_config.transitions] if state_config and state_config.transitions else []
                                transitionsDict = state_config.transitions
                                possibleNextPhases = [await self.state_manager.getPhaseDataByName(ph) for ph in allowed_transitions if ph in workflow_obj.phases.keys()]
                                if await self.state_manager.getIsInPhase():
                                    print(f"[kekw] await self.state_manager.getPhaseData() : ", await self.state_manager.getPhaseData())
                                    phaseData = await self.state_manager.getPhaseData()
                                    possibleNextPhases.append(await self.state_manager.getPhaseDataByName(phaseData["currentPhase"]))
                                    allowed_transitions.append(phaseData["currentPhase"])
                                if not allowed_transitions:
                                    # No transitions available, check if Goodbye state exists and auto-transition
                                    workflow = await self.state_manager.getWorkflow()
                                    workflow_obj = workflow.workflow
                                    print(f"[DEBUG] No transitions from {workflow_state}. Available states: {list(workflow_obj.states.keys())}")
                                    if "Goodbye" in workflow_obj.states:
                                        print(f"[DEBUG] No transitions available from {workflow_state}, auto-transitioning to Goodbye")
                                        await self.state_manager.transitionWorkflow("Goodbye")
                                        continue
                                    else:
                                        # No Goodbye state, complete the workflow
                                        print(f"[DEBUG] No Goodbye state found, completing workflow")
                                        self.status = "completed"
                                        self.reason = f"Workflow completed - no transitions available from state: {workflow_state}"
                                        self.logger.info(f"Workflow completed - no transitions from: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                        return {"status": self.status, "reason": self.reason, "key_events": self.key_events}
                                else:
                                    # Enhanced LLM prompt with variable context
                                    variable_tuples = await self.get_variable_context_for_transitions(allowed_transitions, workflow_state)
                                    variable_context_str = ", ".join([f"{var}={val}" for var, val in variable_tuples]) if variable_tuples else "No variables available"
                                    # function that builds the prompt
                                    llm_prompt = await self.build_workflow_transition_prompt(user_query, workflow_state, intent, variable_context_str, allowed_transitions, transitionsDict, self.state_manager, possibleNextPhases)
                                    print(f"[mohamed] llm_prompt: {llm_prompt}")
                                    decision = await self.evaluate_llm_workflow_transition(llm_prompt, allowed_transitions)
                                    print(f"[kekw] llm prompt : {llm_prompt}")
                                    print(f"[kekw] LLM decision: {decision}")
                                    # if decision not in allowed_transitions:
                                    #     next_workflow_state = "Goodbye"
                                    # else:
                                    #     next_workflow_state = decision
                                    next_workflow_state = decision
                                    await self.state_manager.transitionWorkflow(next_workflow_state)
                                    print(f"current pipeline state: ", await self.state_manager.getCurrentPipelineState())
                                    if await self.state_manager.getCurrentPipelineState() == "processing" and await self.state_manager.isBackChannelingEnabled():
                                        print(f"[DEBUG] Back-channeling enabled, starting worker for message ID {self.messageId}")
                                        asyncio.create_task(self.back_channeling_worker(self.messageId))
                                        self.messageId += 1
                                    if await self.state_manager.getCurrentPipelineState() == "tts":
                                        await self.audioPlayer.incrementUserTurnMessageId()
                                    continue
                        else:
                            self.logger.info(f"LLM requested redo for step {current_step.step}. Aborting after one retry.")
                            return {'status': 'aborted', 'reason': f'LLM requested redo for step {current_step.step}'}
                else:
                    # For other steps, always proceed to next pipeline step
                    next_step_id = None
                    currentPipeLineStepIndex = await self.state_manager.getCurrentPipelineStepIndex()
                    for idx, step in enumerate(current_pipeline):
                        if hasattr(step, "step") and step.step == pipeline_state and idx >= currentPipeLineStepIndex:
                            if idx + 1 < len(current_pipeline):
                                next_step_id = current_pipeline[idx + 1].step
                            break
                    if next_step_id:
                        await self.state_manager.transitionPipeline(next_step_id)
                        print(f"current pipeline state: ", await self.state_manager.getCurrentPipelineState())
                        if await self.state_manager.getCurrentPipelineState() == "processing" and await self.state_manager.isBackChannelingEnabled():
                            print(f"[DEBUG] Back-channeling enabled, starting worker for message ID {self.messageId}")
                            asyncio.create_task(self.back_channeling_worker(self.messageId))
                            self.messageId += 1    
                        if await self.state_manager.getCurrentPipelineState() == "tts":
                            await self.audioPlayer.incrementUserTurnMessageId()    
                        continue
                    else:
                        # End of pipeline, handle workflow transition
                        workflow = await self.state_manager.getWorkflow()
                        workflow_obj = workflow.workflow
                        state_config = workflow_obj.states.get(workflow_state)

                        # Check if this is an end state with no transitions
                        if state_config and state_config.type == "end" and (not state_config.transitions or len(state_config.transitions) == 0):
                            self.status = "completed"
                            self.reason = f"Workflow completed successfully at end state: {workflow_state}"
                            self.logger.info(f"Workflow completed at end state: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                            return {"status": self.status, "reason": self.reason, "key_events": self.key_events}

                        allowed_transitions = [t.target for t in state_config.transitions] if state_config and state_config.transitions else []
                        transitionsDict = state_config.transitions 
                        possibleNextPhases = [await self.state_manager.getPhaseDataByName(ph) for ph in allowed_transitions if ph in workflow_obj.phases.keys()]
                        if await self.state_manager.getIsInPhase():
                            print(f"[kekw] await self.state_manager.getPhaseData() : ", await self.state_manager.getPhaseData())
                            phaseData = await self.state_manager.getPhaseData()
                            possibleNextPhases.append(await self.state_manager.getPhaseDataByName(phaseData["currentPhase"]))
                            allowed_transitions.append(phaseData["currentPhase"])
                        if not allowed_transitions:
                            # No transitions available, check if Goodbye state exists and auto-transition
                            workflow = await self.state_manager.getWorkflow()
                            workflow_obj = workflow.workflow
                            print(f"[DEBUG] No transitions from {workflow_state}. Available states: {list(workflow_obj.states.keys())}")
                            if "Goodbye" in workflow_obj.states:
                                print(f"[DEBUG] No transitions available from {workflow_state}, auto-transitioning to Goodbye")
                                await self.state_manager.transitionWorkflow("Goodbye")
                                continue
                            else:
                                # No Goodbye state, complete the workflow
                                print(f"[DEBUG] No Goodbye state found, completing workflow")
                                self.status = "completed"
                                self.reason = f"Workflow completed - no transitions available from state: {workflow_state}"
                                self.logger.info(f"Workflow completed - no transitions from: {workflow_state}", action="workflow_complete", layer="orchestrator_v3")
                                return {"status": self.status, "reason": self.reason, "key_events": self.key_events}
                        else:
                            # Enhanced LLM prompt with variable context
                            variable_tuples = await self.get_variable_context_for_transitions(allowed_transitions, workflow_state)
                            variable_context_str = ", ".join([f"{var}={val}" for var, val in variable_tuples]) if variable_tuples else "No variables available"
                            # function that builds the prompt
                            llm_prompt = await self.build_workflow_transition_prompt(user_query, workflow_state, intent, variable_context_str, allowed_transitions, transitionsDict, self.state_manager, possibleNextPhases)
                            print(f"[mohamed] llm_prompt: {llm_prompt}")
                            decision = await self.evaluate_llm_workflow_transition(llm_prompt, allowed_transitions)
                            print(f"[kekw] llm prompt : {llm_prompt}")
                            print(f"[kekw] LLM decision: {decision}")
                            # if decision not in allowed_transitions:
                            #     next_workflow_state = "Goodbye"
                            # else:
                            #     next_workflow_state = decision
                            next_workflow_state = decision
                            await self.state_manager.transitionWorkflow(next_workflow_state)
                            print(f"current pipeline state: ", await self.state_manager.getCurrentPipelineState())
                            if await self.state_manager.getCurrentPipelineState() == "processing" and await self.state_manager.isBackChannelingEnabled():
                                print(f"[DEBUG] Back-channeling enabled, starting worker for message ID {self.messageId}")
                                asyncio.create_task(self.back_channeling_worker(self.messageId))
                                self.messageId += 1
                            if await self.state_manager.getCurrentPipelineState() == "tts":
                                await self.audioPlayer.incrementUserTurnMessageId()
                            continue
        except Exception as e:
            # Calculate duration even on error
            orchestrator_duration_ms = (time.perf_counter() - orchestrator_start_time) * 1000

            self.status = "aborted"
            self.reason = f"Exception in orchestrator run: {str(e)}"
            self.logger.error(
                self.reason,
                action="run",
                layer="orchestrator_v3",
                metrics={"duration_ms": orchestrator_duration_ms}
            )
            return {"status": self.status, "reason": self.reason, "key_events": [self.reason]}


    async def evaluate_llm_pipeline_transition(self, prompt):
        start_time = time.perf_counter()

        try:
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )
            decision = response.choices[0].message.content.strip().lower()

            duration_ms = (time.perf_counter() - start_time) * 1000
            self.logger.info(
                "LLM pipeline transition evaluation completed",
                action="evaluate_llm_pipeline_transition",
                output_data={"decision": decision},
                layer="orchestrator_v3",
                metrics={"duration_ms": duration_ms}
            )

            return "proceed" if "proceed" in decision else "redo"
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            self.logger.error(
                f"LLM evaluation failed: {e}",
                action="evaluate_llm_pipeline_transition",
                layer="orchestrator_v3",
                metrics={"duration_ms": duration_ms}
            )
            return "redo"

    def extract_variables_from_condition(self, condition: str) -> List[str]:
        """
        Simple method to extract variable names from a condition string.

        Args:
            condition (str): The condition string (e.g., "amount >= 60000 and balance > 1000")

        Returns:
            List[str]: List of variable names found in the condition
        """
        if condition in ["true", "false"]:
            return []

        # Simple regex to find variable names (letters, numbers, underscore)
        # Exclude common keywords and operators
        variable_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\b'
        matches = re.findall(variable_pattern, condition)

        # Filter out Python keywords and operators
        excluded = {
            'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None',
            'if', 'else', 'elif', 'for', 'while', 'def', 'class'
        }

        variables = [var for var in matches if var not in excluded]
        return variables

    async def get_variable_context_for_transitions(self, allowed_transitions: List[str], workflow_state: str) -> List[tuple]:
        """
        Get variable context for workflow transitions by extracting variables from transition conditions.

        Args:
            allowed_transitions (List[str]): List of allowed transition targets
            workflow_state (str): Current workflow state

        Returns:
            List[tuple]: List of (variable_name, value) tuples
        """
        try:
            # Get workflow and state configuration
            workflow = await self.state_manager.getWorkflow()
            workflow_obj = workflow.workflow
            state_config = workflow_obj.states.get(workflow_state)

            if not state_config or not state_config.transitions:
                return []

            # Extract all variables from all transition conditions
            all_variables = []
            for transition in state_config.transitions:
                variables = self.extract_variables_from_condition(transition.condition)
                all_variables.extend(variables)

            # Remove duplicates
            unique_variables = list(set(all_variables))

            # Get variable values from memory and create tuples
            variable_tuples = []
            for variable in unique_variables:
                # Try different key formats
                key_formats = [
                    f"{self.session_id}_{workflow_state}_{variable}",
                    f"{workflow_state}_{variable}",
                    variable,
                    f"contextual_{variable}"
                ]

                for key in key_formats:
                    value = await self.memory_manager.get(key)
                    if value is not None:
                        variable_tuples.append((variable, value))
                        break

            print("[kekw] variable_tuples: ", variable_tuples)
            return variable_tuples

        except Exception as e:
            self.logger.error(f"Error getting variable context: {e}", action="get_variable_context", layer="orchestrator_v3")
            return []

    async def evaluate_llm_workflow_transition(self, prompt, allowed_transitions):
        start_time = time.perf_counter()

        try:
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "system", "content": "You are a deterministic workflow router. Obey constraints."}, {"role": "user", "content": prompt}],
                max_tokens=50,
                temperature=0.1
            )
            
            transitionResponse = response.choices[0].message.content.strip()
            # decision is the first line
            decision = transitionResponse.split("\n")[0].strip()
            phaseDecision = transitionResponse.split("\n")[1].strip() if len(transitionResponse.split("\n")) > 1 else ""

            print("[kekw] phaseDecision : ", phaseDecision)
            print("[kekw] explaination : ", transitionResponse.split("\n")[2].strip())
            

            # enter logic will be not need since we remove old transation prompt logic
            # if "enter" in phaseDecision:
            #     # Entering a new phase, extract phase name
            #     phase_name = phaseDecision.split("::")[1].strip()
            #     print(f"[kekw] Entering new phase: {phase_name}")
            #     await self.state_manager.enterPhase(phase_name)
            if "exit" in phaseDecision:
                new_phase_name = phaseDecision.split("::")[1].strip()
                print(f"[mohamed] Exiting current phase and entering new phase: {new_phase_name}")
                await self.state_manager.exitPhase()
                # Enter the new phase and update the decision to the initial state of the new phase if available
                await self.state_manager.enterPhase(new_phase_name)
                # Get the initial state of the new phase
                workflow = await self.state_manager.getWorkflow()
                wf_obj = getattr(workflow, "workflow", workflow)
                phase_info = wf_obj.phases.get(new_phase_name) if hasattr(wf_obj, "phases") else None
                if phase_info and hasattr(phase_info, "initial_state"):
                    decision = phase_info.initial_state
                    print(f"[mohamed] Updated decision to initial state of {new_phase_name}: {decision}")
                else:
                    self.logger.warning(f"No initial state found for phase {new_phase_name}, keeping decision: {decision}")

            duration_ms = (time.perf_counter() - start_time) * 1000
            self.logger.info(
                "LLM workflow transition evaluation completed",
                action="evaluate_llm_workflow_transition",
                output_data={"decision": decision, "allowed_transitions": allowed_transitions},
                layer="orchestrator_v3",
                metrics={"duration_ms": duration_ms}
            )

            return decision

            # if decision in allowed_transitions or decision in :
            #     return decision
            # return "Goodbye"
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            self.logger.error(
                f"LLM workflow transition evaluation failed: {e}",
                action="evaluate_llm_workflow_transition",
                layer="orchestrator_v3",
                metrics={"duration_ms": duration_ms}
            )
            return "Goodbye"

    # [CLEANUP] remove call down we do not need it it is being used just for print
    async def _track_conversation_turn(self, step_name: str, output: dict):
        """Track conversation turns for dialog logging"""
        start_time = time.perf_counter()

        try:
            # Capture user message from STT step
            if step_name == "stt_process" and isinstance(output, dict):
                if "text" in output:
                    self.user_message = output["text"]
                    print(f"[DEBUG] Captured user message: {self.user_message}")

            # Capture AI response from TTS step and save conversation turn
            elif step_name == "tts" and isinstance(output, dict):
                if "greeting_response" in output:
                    self.ai_response = output["greeting_response"]
                    print(f"[DEBUG] Captured AI response: {self.ai_response}")

                    # Save the complete conversation turn
                    if self.user_message and self.ai_response:
                        # Get intent from memory
                        intent = await self.memory_manager.get("intent") or "unknown"
                        await self.memory_manager.save_conversation_turn(
                            user_message=self.user_message,
                            ai_message=self.ai_response,
                            intent=intent
                        )
                        print(f"[DEBUG] Saved conversation turn: user='{self.user_message}' ai='{self.ai_response}' intent='{intent}'")

                        # Reset for next turn
                        self.user_message = ""
                        self.ai_response = ""

            duration_ms = (time.perf_counter() - start_time) * 1000
            self.logger.debug(
                "Conversation turn tracking completed",
                action="_track_conversation_turn",
                input_data={"step_name": step_name},
                layer="orchestrator_v3",
                metrics={"duration_ms": duration_ms}
            )

        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            print(f"[DEBUG] Error tracking conversation turn: {e}")
            self.logger.error(
                "Error tracking conversation turn",
                action="_track_conversation_turn",
                input_data={"step_name": step_name},
                reason=str(e),
                layer="orchestrator_v3",
                metrics={"duration_ms": duration_ms}
            )

    # [CLEANUP] remove call down we do not need it it is being used just for print
    async def cleanup(self):
        # Stub for orchestrator cleanup logic
        pass


    async def build_workflow_transition_prompt(
        self,
        user_query: str,
        workflow_state: str,
        intent: str,
        variable_context_str: str,
        allowed_transitions: str,
        transitionsDict: str,
        state_manager,
        possibleNextPhases: str,
    ) -> str:
        """
        Build the LLM prompt for deciding workflow transitions.

        Returns different prompts based on whether the system is currently inside a phase.
        """
        is_in_phase = await state_manager.getIsInPhase()
        phase_data = await state_manager.getPhaseData() if is_in_phase else None
        print(f"[mohamed] is_in_phase: {is_in_phase}, phase_data: {phase_data}")
        if is_in_phase:
            # Gather phase info and exit targets to guide the LLM when at the last state
            phase_name = phase_data.get("currentPhase") if isinstance(phase_data, dict) else None
            phase_exit_targets_str = ""
            phase_states = []
            try:
                workflow = await state_manager.getWorkflow()
                wf_obj = getattr(workflow, "workflow", workflow)
                phase_info = wf_obj.phases.get(phase_name) if phase_name and hasattr(wf_obj, "phases") else None
                if phase_info:
                    # Collect states list for clarity
                    phase_states = list(getattr(phase_info, "states", []) or [])
                    # Build mapping for exit targets -> initial state (if target is a phase)
                    exit_map = {}
                    for t in getattr(phase_info, "transitions", []) or []:
                        tgt = getattr(t, "target", None)
                        if not tgt:
                            continue
                        if hasattr(wf_obj, "phases") and tgt in wf_obj.phases:
                            exit_map[tgt] = getattr(wf_obj.phases[tgt], "initial_state", tgt)
                        else:
                            exit_map[tgt] = tgt
                    phase_exit_targets_str = str(exit_map)
            except Exception:
                pass

            # Phase-specific prompt focusing strictly on phase progression
            return f"""
                You are an AI orchestrator deciding the next state WITHIN THE CURRENT PHASE of a banking conversation.

                Context:
                - User Query: {user_query}
                - Current Workflow State: {workflow_state}
                - Detected Intent: {intent}
                - Variables: {variable_context_str}
                - Current Phase Name: {phase_name}
                - Current Phase States: {phase_states}
                - Current Phase Data: {phase_data}
                - Phase Exit Targets (mapped to initial states if target is a phase): {phase_exit_targets_str}

                Decision Rules (Phase Mode):
                1. You are currently inside a phase. IGNORE Allowed Transitions and Transitions Dictionary.
                2. Select ONLY from the states defined by the current phase.
                3. Pick the state that comes immediately AFTER the current workflow state in the phase's states list when one exists.
                4. If the current state is the LAST state in the phase, you MUST exit. Choose the correct exit target phase from 'Phase Exit Targets' based on the user's intent. The second line of your output should be 'exit::target_phase_name'.
                5. Do not select the Goodbye state unless the user explicitly asks to end the conversation.
                6. Use EXACT state and phase names as provided in the context (e.g., 'exchangeRate', not 'ExchangeRate'; 'ExchangePhase', not 'exchange_phase').
                7. Output EXACTLY three lines: state name, phase command, and reasoning. Do NOT deviate from this format.

                Output:
                - Line 1: EXACT name of the next state.
                - Line 2: A phase command in ONE of these two formats:
                    - `{phase_name}::next_state_name`      --> if transitioning to another state INSIDE the current phase.
                    - `exit::target_phase_name`         --> if EXITING the current phase and entering a new one.
                - Line 3: Brief reasoning.
                """
      