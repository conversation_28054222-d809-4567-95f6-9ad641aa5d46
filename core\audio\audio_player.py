"""
Audio Playing Module for the Voice Agents Platform.

This module provides audio playing utilities with integrated structured logging.
"""

import asyncio
import queue
from core.logging.logger_config import get_module_logger
from schemas.audio_schema import AudioSchema, AudioTag
from pydub import AudioSegment
import simpleaudio as sa
import io
import pygame

class AudioPlayer:

    def __init__(self, session_id: str = None):
        self.session_id = session_id
        self.logger = get_module_logger("audio_player", session_id=session_id)
        self.logger.info(
            "Initialized AudioPlayer",
            action="initialize",
            layer="audio_player",
            session_id=session_id
        )
        self.audioQueue = queue.Queue()
        self.audioPlaying = False
        self.userSpeeking = False
        self.messageId = -1
        self.userTurnMessageId = 0
        self.mainAudioNoPlayed = 0
        self.turn = "ai"

    async def enqueue(self, audioData: AudioSchema):
        self.audioQueue.put(audioData)
        return
    
    async def playAudio(self):
        while True:
            await asyncio.sleep(0.05)
            if not self.audioQueue.empty() and not self.userSpeeking and self.turn == "ai":
                audioData: AudioSchema = self.audioQueue.queue[0]
                if audioData.id < self.messageId:
                    self.audioQueue.get()
                    continue
                self.audioPlaying = True
                await self.play_audio_file(audioData.audio_path, audioData)
                if audioData.id > self.messageId:
                    self.messageId = audioData.id
                if audioData.tag == AudioTag.main:
                    print("[DEBUG] Main audio played")
                    self.mainAudioNoPlayed += 1
                    if self.mainAudioNoPlayed == self.userTurnMessageId:
                        self.turn = "user"
                self.audioPlaying = False
                self.audioQueue.get()
                

    async def play_audio_file(self, audio_path: str, audioData: AudioSchema):
        try:
            # Initialize pygame mixer
            pygame.mixer.init()
            pygame.mixer.music.load(audio_path)
            pygame.mixer.music.play()


            # Wait until playback is finished
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.1)

        except Exception as e:
            print(f"Could not play audio: {e}\nPlease play the file manually: {audio_path}")
        
    
    def isUserSpeeking(self):
        return self.userSpeeking
    
    def isAudioPlaying(self):
        return  self.audioPlaying
    
    def isAudioQueueEmpty(self):
        return self.audioQueue.empty()
    
    async def incrementUserTurnMessageId(self):
        self.userTurnMessageId += 1
        return self.userTurnMessageId
    