from typing import Any, List, Dict, Optional, Union
from pydantic import BaseModel
from asteval import Interpreter

# workflow schema

class Transition(BaseModel):
    condition: str
    target: str

class State(BaseModel):
    id: str
    type: str
    layer2_id: str
    expected_input: List[str]
    expected_output: List[str]
    missing_input_fallback_state: Optional[str] = None
    isFallbackInput: Optional[bool] = False
    transitions: Optional[List[Transition]] = []
    allowed_tools: List[str]
    engagement_message: Optional[str] = None

    # Reversibility fields
    anti_state: Optional[str] = None

class ByPass(BaseModel):
    state: str
    condition: str

class Phase(BaseModel):
    name: str
    states: List[str]
    initial_state: str
    transitions: Optional[List[Transition]] = []
    byPass: Optional[list[ByPass]] = []      

class WorkflowConfig(BaseModel):
    id: str
    name: str
    version: str
    start: str
    allowed_actions:List[str]
    prohibited_actions:List[str]
    engagement_messages: Optional[str] = None
    states: Dict[str, State]
    phases: Dict[str, Phase]

class WorkflowWrapper(BaseModel):
    workflow: WorkflowConfig

# pipeline schema

class Tools(BaseModel):
    memory: Optional[str] = None
    external_tools: str


class PipelineStep(BaseModel):
    step: str
    process: str
    agent: str
    input: Dict[str, str]
    tools: Tools
    output: Dict[str, str]
    expected_output: Optional[List[str]] = None


class OnInterrupt(BaseModel):
    handler: str
    resume_from: str


class OnError(BaseModel):
    retry: int
    fallback_state: str


class Layer2(BaseModel):
    id: str
    version: str
    initial_variable: Optional[Dict[str, Any]] = None
    pipeline: List[PipelineStep]
    backChanneling: bool
    onInterrupt: Optional[OnInterrupt] = None
    onError: Optional[OnError] = None

class InterruptConfig(BaseModel):
    reversible: bool
    side_effects: bool = False
    confirmation_required: bool = False
    interrupt_message: Optional[str] = None
    description: Optional[str] = None