from agents.base.base_agent import BaseAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from core.memory.memory_manager import MemoryManager
from core.logging.logger_config import get_module_logger
import asyncio
import os
import openai
import json
import time
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from utils.audio_utils import synthesize_fallback_audio
import re

logger = get_module_logger("processing_agent")

def extract_json_from_llm_output(output):
    # Remove markdown code block if present
    output = output.strip()
    if output.startswith('```'):
        output = re.sub(r'^```[a-zA-Z]*', '', output)
        output = output.strip('`\n ')
    # Try to find JSON substring
    match = re.search(r'\{.*\}', output, re.DOTALL)
    if match:
        return match.group(0)
    return output

class ProcessingAgent(BaseAgent):
    def __init__(self, session_id=None, state_id=None):
        super().__init__("processing_agent", session_id, state_id)
        self.memory_manager = MemoryManager(session_id)
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)

    def load_config(self):
        config_path = os.path.join(os.path.dirname(__file__), '../../..', 'config', 'state_manager_config.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return {"enable_emotion": True, "enable_gender": True, "enable_disambiguation": False}

    async def _process_llm_response(self, route_result, route_outputs, contextual, persistent):
        """
        Common LLM processing logic used by both normal and reversal flows.
        """
        llm_answer_result = await self.llm_postprocess(route_result, route_outputs, contextual, persistent)
        
        if isinstance(llm_answer_result, dict):
            llm_answer = llm_answer_result.get("answer", "")
            llm_answer_confidence = llm_answer_result.get("confidence", 0.0)
            llm_answer_variable_outputs = llm_answer_result.get("variable_outputs", {})
        else:
            llm_answer = llm_answer_result
            llm_answer_confidence = 0.0
            
        return llm_answer, llm_answer_confidence, llm_answer_variable_outputs

    async def process(self, input_data, context=None):
        start_time = time.perf_counter()
        context = context or {}
        self._log_process_start(input_data, context)
        session_id = self.session_id or context.get("session_id")
        redis_key = session_id
        config = self.load_config()
        try:
            # Check if this is a reversal operation - only handle if user confirmed or cancelled
            undo_context = await self.memory_manager.get("undo_context")
            if undo_context:
                # Get the current intent to see if user confirmed or cancelled
                shared_context = await self.load_context(redis_key) or {}
                current_intent = shared_context.get("intent", "")

                # Only handle reversal if user gave a confirm/cancel response
                if current_intent in ["confirm", "cancel"]:
                    return await self._handle_reversal(undo_context, context, start_time)
                # If intent is still "undo", let normal processing handle it (don't process reversal yet)
            
            # 1. Load the shared context dict (from Redis)
            shared_context = await self.load_context(redis_key) or {}
            fallback_result = await self.handle_redis_fallback(shared_context, session_id)
            if fallback_result:
                return fallback_result
            if isinstance(shared_context, dict) and "tts_response" in shared_context:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Redis unavailable. Fallback triggered.",
                    code=StatusCode.SERVICE_UNAVAILABLE,
                    outputs={"fallback_message": shared_context["tts_response"]},
                    meta={"agent": self.agent_name}
                )
            # 2. Get the preprocessed text
            clean_text = shared_context.get("clean_text")
            if not clean_text:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No clean_text found in shared context",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"agent": self.agent_name}
                )
            # 3. Access contextual and persistent memory 
            contextual = await self.memory_manager.get_conversation()
            persistent = await self.memory_manager.get_all_persistent()
            # 4. Dummy routing logic (simulate internal, DB, or RAG)
            if "account" in clean_text:
                route_result = await self.route_internal_logic(clean_text, contextual, persistent)
            elif "database" in clean_text:
                route_result = await self.route_database(clean_text, contextual, persistent)
            elif "knowledge" in clean_text or "rag" in clean_text:
                route_result = await self.route_rag_engine(clean_text, contextual, persistent)
            else:
                route_result = await self.route_internal_logic(clean_text, contextual, persistent)
            # 5. Publish notification
            llm_answer, llm_answer_confidence, llm_answer_variable_outputs = await self._process_llm_response(route_result, input_data.get("expected_output"), contextual, persistent)
            # Generate business logic outputs based on intent
            intent = shared_context.get("intent", "unknown")
            business_outputs = await self.generate_business_outputs(intent, clean_text, route_result)

            result = {
                "route": route_result.get("route"),
                "llm_answer": llm_answer,
                "llm_answer_confidence": llm_answer_confidence,
                "variable_outputs": llm_answer_variable_outputs,
                **business_outputs
            }
            # Add processing_confidence_value (same as llm_answer_confidence)
            result["processing_confidence_value"] = llm_answer_confidence
            # Save user and AI turn to contextual memory for dialog history
            await self.save_conversation_turn(clean_text, llm_answer, shared_context.get("intent"))
            # RELOAD the latest context from Redis to get the updated conversation
            shared_context = await self.load_context(redis_key) or {}
            if not isinstance(shared_context, dict):
                print("[ERROR] shared_context is not a dict! Reinitializing as dict.")
                shared_context = {}
            print("[DEBUG] shared_context type before update:", type(shared_context))
            print("[DEBUG] result type:", type(result))
            shared_context.update(result)
            duration_ms = int((time.perf_counter() - start_time) * 1000)
            shared_context["latencyProcessing"] = duration_ms
            # Final type check before saving
            if not isinstance(shared_context, dict):
                print("[ERROR] Attempted to save non-dict context! Forcing dict.")
                shared_context = dict(shared_context)
            await self.save_context(redis_key, shared_context)
            # 7. Publish notification
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "complete"},
                context_keys_updated=list(result.keys()) + ["latencyProcessing"]
            )
            # await self.publish_notification("agent_completion", notification.to_json())
            # 8. Return result
            result["latencyProcessing"] = duration_ms
            result = StateOutput(
                status=StatusType.SUCCESS,
                message="Processing complete",
                code=StatusCode.OK,
                outputs=result,
                meta={
                    "agent": self.agent_name,
                    "latencyProcessing": duration_ms,
                    "contextual_length": len(contextual),
                    "persistent_keys": list(persistent.keys())
                }
            )
            self._log_process_end(result, duration_ms)
            return result
        except Exception as e:
            duration_ms = int((time.perf_counter() - start_time) * 1000)
            self._log_error(e, input_data)
            # Notify orchestrator of error
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "error", "error_message": str(e)},
                context_keys_updated=[]
            )
            # await self.publish_notification("agent_completion", notification.to_json())
            # Generate fallback audio
            fallback_text = "Sorry, something went wrong. Can you repeat that please?"
            audio_path = synthesize_fallback_audio(fallback_text, session_id=session_id or "fallback")
            outputs = {"fallback_message": fallback_text}
            if audio_path:
                outputs["audio_path"] = audio_path
            return StateOutput(
                status=StatusType.ERROR,
                message=f"ProcessingAgent error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencyProcessing": duration_ms,
                    "error": str(e)
                }
            )
        
        # TODO: Implement actual internal logic here
    async def route_internal_logic(self, clean_text, contextual, persistent):
        # Dummy internal logic
        return {"route": "internal_logic", "data": f"Account info for: {clean_text}"}

        # TODO: Implement actual database logic here
    async def route_database(self, clean_text, contextual, persistent):
        # Dummy DB logic
        return {"route": "database", "data": f"Fetched from DB: {clean_text}"}

        # TODO: Implement actual RAG engine logic here
    async def route_rag_engine(self, clean_text, contextual, persistent):
        # Dummy RAG logic
        return {"route": "rag_engine", "data": f"RAG answer for: {clean_text}"}

    async def generate_business_outputs(self, intent, clean_text, route_result):
        """Generate business logic outputs based on intent and context."""
        outputs = {}

        # Generate placeholder outputs based on intent
        if intent == "check_balance" or "balance" in clean_text.lower():
            outputs["account_balance"] = "$2,547.83"
        else:
            outputs["account_balance"] = None

        if intent == "loan_inquiry" or "loan" in clean_text.lower():
            outputs["loan_eligibility"] = "Pre-approved for up to $50,000"
        else:
            outputs["loan_eligibility"] = None

        # Always provide exit signal for workflow completion
        if intent in ["goodbye", "exit", "end"] or any(word in clean_text.lower() for word in ["goodbye", "bye", "thanks", "thank you"]):
            outputs["exit_signal"] = "conversation_complete"
        else:
            outputs["exit_signal"] = "continue"

        return outputs

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def llm_postprocess(self, route_result, route_outputs, contextual, persistent):
        # update the prompt to populate variables
        # the variables should be passes as a part of a variable called "route_outputs"
        # for now make the "route_outputs" consist of 2 variables that need to be populated, from_currency, to_curreny.
        print("[kekw] route_result: ", route_result)
        print("[kekw] route_outputs: ", route_outputs)
        prompt = (
            "You are an AI assistant. Given the following routed result, contextual memory, and persistent memory, "
            "generate a helpful, user-facing answer. Also populate the expected outputs. Respond in JSON: {\"answer\": <answer>, \"confidence\": <float between 0 and 1>, \"outputs\": <outputs of the expected outputs in JSON>}\n"
            f"Routed Result: {route_result}\n"
            f"Contextual Memory: {contextual}\n"
            f"Persistent Memory: {persistent}\n"
            f"Route Outputs: {route_outputs}\n"
            "Answer:"
        )
        print("[kekw] prompt : ", prompt)
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=128,
                temperature=0.1
            )
            import json
            raw = response.choices[0].message.content.strip()
            try:
                json_str = extract_json_from_llm_output(raw)
                result = json.loads(json_str)
                return {
                    "answer": result.get("answer", "No answer available."),
                    "confidence": float(result.get("confidence", 0.0)),
                    "variable_outputs": result.get("outputs", {})
                }
            except Exception:
                return {"answer": raw, "confidence": 0.0}
        except Exception as e:
            self.logger.warning(f"LLM postprocess failed: {e}")
            return {"answer": route_result.get("data", "No answer available."), "confidence": 0.0} 

    async def _handle_reversal(self, undo_context: dict, context: dict, start_time: float) -> StateOutput:
        """
        Handle reversal operations by analyzing the original action and generating appropriate responses.
        """
        try:
            # Simple check: if no undo_context, return "nothing to undo" message
            if not undo_context:
                duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
                result = {
                    "route": "no_undo_available",
                    "llm_answer": "There is nothing to undo right now. Is there anything else I can help you with?",
                    "llm_answer_confidence": 1.0,
                    "processing_confidence_value": 1.0,
                    "latencyProcessing": duration_ms
                }
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="No reversible action available",
                    code=StatusCode.OK,
                    outputs=result,
                    meta={
                        "agent": self.agent_name,
                        "operation": "no_undo_available",
                        "latency": duration_ms
                    }
                )

            original_state = undo_context.get("original_state")
            original_outputs = undo_context.get("original_outputs", {})

            # Check intent before executing reversal
            session_id = self.session_id or context.get("session_id")
            shared_context = await self.load_context(session_id) or {}
            current_intent = shared_context.get("intent", "")

            if current_intent == "confirm":
                # Execute actual reversal business logic here
                pass  # TODO: Add actual reversal logic

                # Provide direct confirmation message instead of calling LLM
                llm_answer = "Reversal confirmed. Processing the transaction reversal now."
                llm_answer_confidence = 1.0

            elif current_intent == "cancel":
                # Don't execute reversal, don't call LLM - CancelReverse state will send user message
                llm_answer = "Processing cancellation"  
                llm_answer_confidence = 1.0

            else:
                # Unknown intent - default response
                llm_answer = "Please say confirm or cancel"
                llm_answer_confidence = 1.0

            # Clear the undo context after processing
            await self.memory_manager.set("contextual", "undo_context", None)
            
            # Calculate processing time
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            result = {
                "route": "reversal",
                "llm_answer": llm_answer,
                "llm_answer_confidence": llm_answer_confidence,
                "processing_confidence_value": llm_answer_confidence,
                "latencyProcessing": duration_ms,
                "reversal_status": "completed",
                "original_state": original_state
            }

            return StateOutput(
                status=StatusType.SUCCESS,
                message="Reversal completed successfully",
                code=StatusCode.OK,
                outputs=result,
                meta={
                    "agent": self.agent_name,
                    "operation": "reversal",
                    "latency": duration_ms
                }
            )
            
        except Exception as e:
            logger.error(f"Error handling reversal: {e}")
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Reversal failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"agent": self.agent_name, "operation": "reversal"}
            )

 